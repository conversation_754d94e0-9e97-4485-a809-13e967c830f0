import {
	ActionIcon,
	Button,
	Flex,
	Group,
	Stack,
	Text,
	ThemeIcon,
	Title,
	Tooltip,
} from "@mantine/core";
import {
	IconCheck,
	IconCameraRotate,
	IconInfoCircle,
	IconLoader2,
	IconPlayerPause,
	IconPlayerPlay,
	IconRotateClockwise,
	IconSquare,
	IconVideo,
} from "@tabler/icons-react";
import { AnimatePresence, motion } from "framer-motion";
import { useCallback, useEffect, useRef, useState } from "react";
import { MAX_RECORDING_TIME, videoTypeLabel } from "../../constants";
import type { videoDataType } from "../../types";
import Instruction from "../video-instruction/Instruction";
import VideoInstructionsModal from "../video-instruction/VideoInstructionsModal";

type RecordingState = "idle" | "loading" | "recording" | "recorded";

type RecordVideoModalProps = {
	videoType: videoDataType;
	onVideoRecord: (blob: Blob) => void;
};

const VideoRecorder = (props: RecordVideoModalProps) => {
	const [recordingState, setRecordingState] =
		useState<RecordingState>("idle");
	const [isActuallyRecording, setIsActuallyRecording] = useState(false);
	const [recordingTime, setRecordingTime] = useState(0);
	const [isPaused, setIsPaused] = useState(false);
	const [stream, setStream] = useState<MediaStream | null>(null);
	const [isFlipped, setIsFlipped] = useState(false);
	const videoRef = useCallback(
		(node: HTMLVideoElement) => {
			if (node !== null && stream) {
				node.srcObject = stream;
			}
		},
		[stream]
	);
	const [recordedBlob, setRecordedBlob] = useState<Blob | null>(null);
	const [recordedVideoUrl, setRecordedVideoUrl] = useState<string>("");
	const mediaRecorderRef = useRef<MediaRecorder | null>(null);
	const intervalRef = useRef<NodeJS.Timeout | null>(null);
	const streamRef = useRef<MediaStream | null>(null);
	const canvasRef = useRef<HTMLCanvasElement | null>(null);
	const animationFrameRef = useRef<number | null>(null);
	const [permissionError, setPermissionError] = useState<string | null>(null);
	const [showInstructions, setShowInstructions] = useState<boolean>(false);

	useEffect(() => {
		streamRef.current = stream;
	}, [stream]);

	useEffect(() => {
		return () => {
			if (streamRef.current) {
				streamRef.current.getTracks().forEach(track => track.stop());
			}
			if (intervalRef.current) {
				clearInterval(intervalRef.current);
			}
			if (animationFrameRef.current) {
				cancelAnimationFrame(animationFrameRef.current);
			}
		};
	}, []);

	useEffect(() => {
		if (recordedBlob) {
			const url = URL.createObjectURL(recordedBlob);
			setRecordedVideoUrl(url);

			return () => {
				URL.revokeObjectURL(url);
				setRecordedVideoUrl("");
			};
		}
	}, [recordedBlob]);

	const formatTime = (timeInSeconds: number) => {
		const minutes = Math.floor(timeInSeconds / 60);
		const seconds = timeInSeconds % 60;
		return `${String(minutes).padStart(2, "0")}:${String(seconds).padStart(
			2,
			"0"
		)}`;
	};

	const drawVideoToCanvas = useCallback(
		(video: HTMLVideoElement, canvas: HTMLCanvasElement) => {
			const ctx = canvas.getContext("2d");
			if (!ctx || video.videoWidth === 0 || video.videoHeight === 0)
				return;

			// Set canvas dimensions to match video
			canvas.width = video.videoWidth;
			canvas.height = video.videoHeight;

			// Clear canvas
			ctx.clearRect(0, 0, canvas.width, canvas.height);

			if (isFlipped) {
				// Flip the canvas horizontally
				ctx.save();
				ctx.scale(-1, 1);
				ctx.drawImage(video, -canvas.width, 0, canvas.width, canvas.height);
				ctx.restore();
			} else {
				// Draw normally
				ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
			}

			// Continue animation loop
			animationFrameRef.current = requestAnimationFrame(() =>
				drawVideoToCanvas(video, canvas)
			);
		},
		[isFlipped]
	);

	const getRecordingStream = useCallback(() => {
		if (!stream) return null;

		if (isFlipped && canvasRef.current) {
			// Get the video element to draw from
			const videoElement = document.querySelector('video[autoplay]') as HTMLVideoElement;
			if (videoElement && videoElement.videoWidth > 0) {
				// Start drawing to canvas
				drawVideoToCanvas(videoElement, canvasRef.current);

				// Get canvas stream and combine with audio from original stream
				const canvasStream = canvasRef.current.captureStream(30);
				const audioTracks = stream.getAudioTracks();

				// Create combined stream with canvas video and original audio
				const combinedStream = new MediaStream([
					...canvasStream.getVideoTracks(),
					...audioTracks
				]);

				return combinedStream;
			}
		}

		return stream;
	}, [stream, isFlipped, drawVideoToCanvas]);

	const stopRecording = useCallback(() => {
		if (
			mediaRecorderRef.current &&
			(mediaRecorderRef.current.state === "recording" ||
				mediaRecorderRef.current.state === "paused")
		) {
			mediaRecorderRef.current.stop();
		}
		if (intervalRef.current) {
			clearInterval(intervalRef.current);
		}
		setIsPaused(false);
		setIsActuallyRecording(false);
	}, []);

	const pauseRecording = useCallback(() => {
		if (
			mediaRecorderRef.current &&
			mediaRecorderRef.current.state === "recording"
		) {
			mediaRecorderRef.current.pause();
			setIsPaused(true);
			if (intervalRef.current) {
				clearInterval(intervalRef.current);
			}
		}
	}, []);

	const resumeRecording = useCallback(() => {
		if (
			mediaRecorderRef.current &&
			mediaRecorderRef.current.state === "paused"
		) {
			mediaRecorderRef.current.resume();
			setIsPaused(false);
			// Resume the timer
			intervalRef.current = setInterval(() => {
				setRecordingTime(prev => {
					if (prev >= MAX_RECORDING_TIME - 1) {
						stopRecording();
						return prev;
					}
					return prev + 1;
				});
			}, 1000);
		}
	}, [stopRecording]);

	const prepareRecording = useCallback(async () => {
		try {
			setRecordingState("loading");

			const mediaStream = await navigator.mediaDevices.getUserMedia({
				video: true,
				audio: true,
			});

			setPermissionError(null);
			setStream(mediaStream);

			// Create canvas for potential flipping
			const canvas = document.createElement("canvas");
			canvasRef.current = canvas;

			setRecordingState("recording");
		} catch (error) {
			setRecordingState("idle");
			if ((error as Error).name === "NotAllowedError") {
				setPermissionError(`Permission to access the camera and microphone was
						denied. Please allow access in your browser settings and
						try again.`);
			} else if ((error as Error).name === "NotReadableError") {
				setPermissionError(
					`The camera or microphone is already in use by another application. Please
						close the other application and try again.`
				);
			} else if ((error as Error).name === "NotFoundError") {
				setPermissionError(
					`No camera or microphone found on this device.`
				);
			} else {
				setPermissionError(
					`We couldn’t access your camera. Please check your device and try again.`
				);
			}
			console.error("Error accessing camera:", error);
		}
	}, []);

	const startActualRecording = useCallback(() => {
		// Create new MediaRecorder with appropriate stream (flipped or original)
		const recordingStream = getRecordingStream();
		if (recordingStream) {
			const mediaRecorder = new MediaRecorder(recordingStream);
			mediaRecorderRef.current = mediaRecorder;

			const chunks: BlobPart[] = [];
			mediaRecorder.ondataavailable = event => {
				chunks.push(event.data);
			};

			mediaRecorder.onstop = () => {
				const blob = new Blob(chunks, {
					type: "video/webm",
				});
				setRecordedBlob(blob);
				setRecordingState("recorded");
				if (stream) {
					stream.getTracks().forEach(track => track.stop());
				}
				setStream(null);
				setIsActuallyRecording(false);

				// Clean up canvas animation
				if (animationFrameRef.current) {
					cancelAnimationFrame(animationFrameRef.current);
					animationFrameRef.current = null;
				}
			};

			mediaRecorder.start();
			setIsActuallyRecording(true);
			setRecordingTime(0);
			intervalRef.current = setInterval(() => {
				setRecordingTime(prev => {
					if (prev >= MAX_RECORDING_TIME - 1) {
						stopRecording();
						return prev;
					}
					return prev + 1;
				});
			}, 1000);
		}
	}, [stopRecording, getRecordingStream, stream]);

	const handleSubmit = () => {
		if (recordedBlob) {
			props?.onVideoRecord(recordedBlob);
			handleClose();
		}
	};

	const handleClose = () => {
		setRecordingState("idle");
		setRecordingTime(0);
		setRecordedBlob(null);
		setPermissionError(null);
		setStream(null);
		setIsPaused(false);
		setIsActuallyRecording(false);
		setIsFlipped(false);

		// Clean up canvas animation
		if (animationFrameRef.current) {
			cancelAnimationFrame(animationFrameRef.current);
			animationFrameRef.current = null;
		}
	};

	const renderIdleState = () => (
		<motion.div
			key="idle"
			initial={{ opacity: 0, scale: 0.95 }}
			animate={{ opacity: 1, scale: 1 }}
			exit={{ opacity: 0, scale: 0.95 }}
			transition={{ duration: 0.3 }}
		>
			<Stack align="center" mt={20}>
				<VideoInstructionsModal
					opened={showInstructions}
					onClose={() => setShowInstructions(false)}
					videoType={props.videoType}
				/>
				<ThemeIcon
					size="5rem"
					variant="light"
					color="blue"
					radius={"lg"}
				>
					<IconVideo size={"3rem"} />
				</ThemeIcon>
				<Title order={3}>Ready to Record</Title>
				{props.videoType && (
					<Flex align={"center"} justify={"center"} gap={5}>
						<Text c={"gray.6"}>
							Recording video for:{" "}
							<span className="font-semibold">
								{
									videoTypeLabel[
										props.videoType as keyof typeof videoTypeLabel
									]
								}
							</span>
						</Text>
						<Button
							size="sm"
							variant="white"
							styles={{ root: { padding: 0 } }}
							onClick={() => setShowInstructions(true)}
						>
							<IconInfoCircle size={"1.25rem"} />
						</Button>
					</Flex>
				)}
				{permissionError ? (
					<Text c="red.6" ta="center" maw={"32rem"}>
						{permissionError}
					</Text>
				) : (
					<Text c={"gray.6"} mt={5}>
						Click the button below to start recording your video
					</Text>
				)}
				<Button
					w={"100%"}
					leftSection={<IconVideo size={20} />}
					variant="light"
					radius={"md"}
					onClick={prepareRecording}
				>
					Start Recording
				</Button>
			</Stack>
		</motion.div>
	);

	const renderLoadingState = () => (
		<motion.div
			key="loading"
			initial={{ opacity: 0, scale: 0.95 }}
			animate={{ opacity: 1, scale: 1 }}
			exit={{ opacity: 0, scale: 0.95 }}
			transition={{ duration: 0.2, ease: "easeOut" }}
		>
			<Stack align="center" mt={20}>
				<ThemeIcon
					size="5rem"
					variant="light"
					color="blue"
					radius={"lg"}
				>
					<IconLoader2 size={"3rem"} className="animate-spin" />
				</ThemeIcon>
				<Title order={3}>Preparing Camera...</Title>
				<Text c={"gray.6"} ta="center">
					Please wait while we set up your camera and microphone
				</Text>
			</Stack>
		</motion.div>
	);

	const renderRecordingState = () => (
		<motion.div
			key="recording"
			initial={{ opacity: 0, scale: 0.95 }}
			animate={{ opacity: 1, scale: 1 }}
			exit={{ opacity: 0, scale: 0.95 }}
			transition={{ duration: 0.3 }}
		>
			<Flex gap={20} mt={10}>
				<Stack style={{ textAlign: "center" }}>
					<Stack pos={"relative"} align={"center"}>
						<video
							ref={videoRef}
							autoPlay
							muted
							className="w-full max-w-sm mx-auto rounded-lg shadow-md"
							style={{
								transform: isFlipped
									? "scaleX(-1)"
									: "scaleX(1)",
								transition: "transform 0.3s ease",
							}}
						/>

						<Tooltip label="Flip Camera">
							<ActionIcon
								variant="filled"
								onClick={() => setIsFlipped(!isFlipped)}
								style={{
									position: "absolute",
									top: 8,
									right: 8,
								}}
								size={"md"}
								radius={"md"}
								color={"blue.6"}
							>
								<IconCameraRotate size={20} />
							</ActionIcon>
						</Tooltip>
					</Stack>

					{isActuallyRecording && (
						<Group justify="center" align="center" mb={2}>
							<div
								className={`w-3 h-3 rounded-full ${isPaused ? "bg-yellow-500" : "bg-red-500 animate-pulse"}`}
							></div>
							<span className="font-mono text-lg">
								{formatTime(recordingTime)} /{" "}
								{formatTime(MAX_RECORDING_TIME)}
								{isPaused && " (Paused)"}
							</span>
						</Group>
					)}

					<Group w={"100%"}>
						{isActuallyRecording ? (
							<>
								{isPaused ? (
									<Button
										variant="light"
										flex={1}
										leftSection={
											<IconPlayerPlay size={16} />
										}
										onClick={resumeRecording}
									>
										Resume
									</Button>
								) : (
									<Button
										variant="outline"
										flex={1}
										leftSection={
											<IconPlayerPause size={16} />
										}
										onClick={pauseRecording}
									>
										Pause
									</Button>
								)}
								<Button
									variant="danger"
									flex={1}
									leftSection={<IconSquare size={16} />}
									onClick={stopRecording}
								>
									Stop
								</Button>
							</>
						) : (
							<Button
								variant="light"
								flex={1}
								leftSection={<IconPlayerPlay size={16} />}
								onClick={startActualRecording}
							>
								Start
							</Button>
						)}
					</Group>
				</Stack>

				<Stack maw={"28rem"}>
					<Instruction videoType={props.videoType} />
				</Stack>
			</Flex>
		</motion.div>
	);

	const renderRecordedState = () => (
		<motion.div
			key="recorded"
			initial={{ opacity: 0, scale: 0.95 }}
			animate={{ opacity: 1, scale: 1 }}
			exit={{ opacity: 0, scale: 0.95 }}
			transition={{ duration: 0.3 }}
		>
			<div className="text-center py-4">
				{recordedVideoUrl && (
					<video
						src={recordedVideoUrl}
						controls
						className="w-full max-w-md mx-auto rounded-lg mb-4"
					/>
				)}
				<Group align="center" justify="center">
					<Button
						onClick={() => {
							setRecordingState("idle");
							setRecordedBlob(null);
							setRecordingTime(0);
							setPermissionError(null);
							setIsPaused(false);
							setIsActuallyRecording(false);

							// Clean up canvas animation
							if (animationFrameRef.current) {
								cancelAnimationFrame(animationFrameRef.current);
								animationFrameRef.current = null;
							}
						}}
						variant="outline"
						className="flex-1"
						leftSection={<IconRotateClockwise size={16} />}
					>
						Record Again
					</Button>
					<Button
						onClick={handleSubmit}
						leftSection={<IconCheck size={16} />}
						className="flex-1"
					>
						Keep this video
					</Button>
				</Group>
			</div>
		</motion.div>
	);

	return (
		<AnimatePresence mode="wait">
			{recordingState === "idle" && renderIdleState()}
			{recordingState === "loading" && renderLoadingState()}
			{recordingState === "recording" && renderRecordingState()}
			{recordingState === "recorded" && renderRecordedState()}
		</AnimatePresence>
	);
};

export default VideoRecorder;
